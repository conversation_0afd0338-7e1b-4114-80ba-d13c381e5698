package com.kerryprops.kip.audit;

import lombok.Data;

/**
 * Request information for audit context.
 * Contains HTTP request details extracted from headers and used for
 * audit event enrichment.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> 2025-07-01 16:39:40
 */
@Data
public class AuditRequestInfo {

    /**
     * Client IP address.
     */
    private String ipAddress;

    /**
     * Client user agent string.
     */
    private String userAgent;

    /**
     * Conversation ID for request tracking.
     */
    private String conversationId;

    /**
     * Correlation ID for distributed tracing.
     */
    private String correlationId;

    /**
     * UI model or page identifier.
     */
    private String uiModel;

    /**
     * Audit filter key for custom filtering.
     */
    private String auditFilterKey;

    /**
     * Audit filter value for custom filtering.
     */
    private String auditFilterValue;

}
