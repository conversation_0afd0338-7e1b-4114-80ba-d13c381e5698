package com.kerryprops.kip.audit.mybatis;

import com.kerryprops.kip.audit.AuditEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.mapping.MappedStatement;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.lang.Nullable;

import java.lang.reflect.Field;
import java.util.Collection;
import java.util.Map;

/**
 * MyBatis实体提取器
 * 从MyBatis参数中提取实体对象用于审计
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
public class MyBatisEntityExtractor {

    private final MyBatisAuditProperties mybatisAuditProperties;

    /**
     * 从MyBatis参数中提取实体对象
     *
     * @param parameter       MyBatis参数对象
     * @param mappedStatement MyBatis映射语句
     * @return 提取的实体对象，如果无法提取则返回null
     */
    @Nullable
    public Object extractEntity(@Nullable Object parameter, @Nullable MappedStatement mappedStatement) {
        if (parameter == null) {
            log.debug("Parameter is null for statement: {}",
                    mappedStatement != null ? mappedStatement.getId() : "unknown");
            return null;
        }

        log.debug("Extracting entity from parameter type: {} for statement: {}",
                parameter.getClass().getSimpleName(),
                mappedStatement != null ? mappedStatement.getId() : "unknown");

        // 1. 直接检查参数是否为实体对象
        if (isAuditableEntity(parameter)) {
            log.debug("Parameter is directly an auditable entity: {}", parameter.getClass().getSimpleName());
            return parameter;
        }

        // 2. 如果参数是Map，尝试从Map中提取实体
        if (parameter instanceof Map) {
            Object entity = extractFromMap((Map<?, ?>) parameter);
            if (entity != null) {
                log.debug("Extracted entity from Map: {}", entity.getClass().getSimpleName());
                return entity;
            }
        }

        // 3. 如果参数是Collection，尝试从Collection中提取实体
        if (parameter instanceof Collection) {
            Object entity = extractFromCollection((Collection<?>) parameter);
            if (entity != null) {
                log.debug("Extracted entity from Collection: {}", entity.getClass().getSimpleName());
                return entity;
            }
        }

        // 4. 尝试通过反射从复杂对象中提取实体
        Object entity = extractFromComplexObject(parameter);
        if (entity != null) {
            log.debug("Extracted entity from complex object: {}", entity.getClass().getSimpleName());
            return entity;
        }

        log.debug("No auditable entity found in parameter for statement: {}",
                mappedStatement != null ? mappedStatement.getId() : "unknown");
        return null;
    }

    /**
     * 从Map参数中提取实体对象
     */
    @Nullable
    private Object extractFromMap(Map<?, ?> parameterMap) {
        log.debug("extractFromMap() - Entry: map size={}, keyTypes={}",
                parameterMap.size(),
                parameterMap.keySet().stream()
                    .map(k -> k != null ? k.getClass().getSimpleName() : "null")
                    .distinct()
                    .toArray());

        if (parameterMap.isEmpty()) {
            log.debug("extractFromMap() - Map is empty, returning null");
            return null;
        }

        // 检查常见的参数名
        log.trace("extractFromMap() - Checking common entity keys: {}",
                mybatisAuditProperties.getCommonEntityKeys());

        for (String commonKey : mybatisAuditProperties.getCommonEntityKeys()) {
            if (parameterMap.containsKey(commonKey)) {
                Object value = parameterMap.get(commonKey);
                log.trace("extractFromMap() - Found common key '{}' with value type: {}",
                        commonKey, value != null ? value.getClass().getSimpleName() : "null");

                if (isAuditableEntity(value)) {
                    log.debug("extractFromMap() - Found auditable entity via common key '{}': {}",
                            commonKey, value.getClass().getSimpleName());
                    return value;
                }
            } else {
                log.trace("extractFromMap() - Common key '{}' not found in map", commonKey);
            }
        }

        // 遍历所有值，查找可审计的实体
        log.trace("extractFromMap() - Scanning all map values for auditable entities");
        int valueIndex = 0;
        for (Map.Entry<?, ?> entry : parameterMap.entrySet()) {
            Object key = entry.getKey();
            Object value = entry.getValue();
            valueIndex++;

            log.trace("extractFromMap() - Checking value #{} (key={}): {}",
                    valueIndex,
                    key != null ? key.toString() : "null",
                    value != null ? value.getClass().getSimpleName() : "null");

            if (isAuditableEntity(value)) {
                log.debug("extractFromMap() - Found auditable entity at key '{}': {}",
                        key, value.getClass().getSimpleName());
                return value;
            }

            // 递归检查嵌套对象
            if (value instanceof Map) {
                log.trace("extractFromMap() - Recursively checking nested Map at key '{}'", key);
                Object nestedEntity = extractFromMap((Map<?, ?>) value);
                if (nestedEntity != null) {
                    log.debug("extractFromMap() - Found auditable entity in nested Map at key '{}': {}",
                            key, nestedEntity.getClass().getSimpleName());
                    return nestedEntity;
                }
            } else if (value instanceof Collection) {
                log.trace("extractFromMap() - Recursively checking nested Collection at key '{}'", key);
                Object nestedEntity = extractFromCollection((Collection<?>) value);
                if (nestedEntity != null) {
                    log.debug("extractFromMap() - Found auditable entity in nested Collection at key '{}': {}",
                            key, nestedEntity.getClass().getSimpleName());
                    return nestedEntity;
                }
            }
        }

        log.debug("extractFromMap() - Exit: No auditable entity found in map with {} entries",
                parameterMap.size());
        return null;
    }

    /**
     * 从Collection参数中提取实体对象
     */
    @Nullable
    private Object extractFromCollection(Collection<?> collection) {
        log.debug("extractFromCollection() - Entry: collection size={}, type={}",
                collection.size(), collection.getClass().getSimpleName());

        if (collection.isEmpty()) {
            log.warn("extractFromCollection() - Collection is empty, returning null");
            return null;
        }

        // 检查集合大小是否超过配置的最大批量大小
        if (collection.size() > mybatisAuditProperties.getMaxBatchSize()) {
            log.warn("extractFromCollection() - Collection size ({}) exceeds maxBatchSize ({}), " +
                    "but proceeding with first element analysis",
                    collection.size(), mybatisAuditProperties.getMaxBatchSize());
        }

        // 取第一个元素进行检查（批量操作通常实体类型相同）
        Object firstElement = collection.iterator().next();
        log.trace("extractFromCollection() - Analyzing first element: {}",
                firstElement != null ? firstElement.getClass().getSimpleName() : "null");

        if (isAuditableEntity(firstElement)) {
            log.debug("extractFromCollection() - First element is auditable entity: {}",
                    firstElement.getClass().getSimpleName());
            return firstElement;
        }

        // 如果第一个元素是Map，尝试从中提取
        if (firstElement instanceof Map) {
            log.trace("extractFromCollection() - First element is Map, attempting recursive extraction");
            Object extractedEntity = extractFromMap((Map<?, ?>) firstElement);
            if (extractedEntity != null) {
                log.debug("extractFromCollection() - Found auditable entity in first element Map: {}",
                        extractedEntity.getClass().getSimpleName());
                return extractedEntity;
            } else {
                log.trace("extractFromCollection() - No auditable entity found in first element Map");
            }
        } else if (firstElement instanceof Collection) {
            log.trace("extractFromCollection() - First element is nested Collection, attempting recursive extraction");
            Object extractedEntity = extractFromCollection((Collection<?>) firstElement);
            if (extractedEntity != null) {
                log.debug("extractFromCollection() - Found auditable entity in nested Collection: {}",
                        extractedEntity.getClass().getSimpleName());
                return extractedEntity;
            } else {
                log.trace("extractFromCollection() - No auditable entity found in nested Collection");
            }
        } else {
            log.trace("extractFromCollection() - First element is neither auditable entity nor container type: {}",
                    firstElement != null ? firstElement.getClass().getSimpleName() : "null");
        }

        log.debug("extractFromCollection() - Exit: No auditable entity found in collection of {} elements",
                collection.size());
        return null;
    }

    /**
     * 从复杂对象中通过反射提取实体
     */
    @Nullable
    private Object extractFromComplexObject(Object parameter) {
        Class<?> parameterClass = parameter.getClass();
        log.debug("extractFromComplexObject() - Entry: analyzing object type={}, package={}",
                parameterClass.getSimpleName(),
                parameterClass.getPackage() != null ? parameterClass.getPackage().getName() : "null");

        // 跳过基本类型和常见的非实体类型
        if (isSimpleType(parameterClass)) {
            log.debug("extractFromComplexObject() - Skipping simple type: {}", parameterClass.getSimpleName());
            return null;
        }

        long startTime = System.currentTimeMillis();
        try {
            Field[] fields = parameterClass.getDeclaredFields();
            log.debug("extractFromComplexObject() - Found {} fields to analyze in class {}",
                    fields.length, parameterClass.getSimpleName());

            if (fields.length == 0) {
                log.trace("extractFromComplexObject() - No fields found in class {}", parameterClass.getSimpleName());
                return null;
            }

            int fieldIndex = 0;
            for (Field field : fields) {
                fieldIndex++;

                // 检查是否超时
                long currentTime = System.currentTimeMillis();
                if (currentTime - startTime > mybatisAuditProperties.getEntityExtractionTimeoutMs()) {
                    log.warn("extractFromComplexObject() - Extraction timeout ({} ms) exceeded while processing field {} of {}, aborting",
                            mybatisAuditProperties.getEntityExtractionTimeoutMs(), fieldIndex, fields.length);
                    break;
                }

                try {
                    field.setAccessible(true);
                    Object fieldValue = field.get(parameter);

                    log.trace("extractFromComplexObject() - Field #{}/{}: name='{}', type={}, value={}",
                            fieldIndex, fields.length, field.getName(), field.getType().getSimpleName(),
                            fieldValue != null ? fieldValue.getClass().getSimpleName() : "null");

                    if (isAuditableEntity(fieldValue)) {
                        log.debug("extractFromComplexObject() - Found auditable entity in field '{}': {}",
                                field.getName(), fieldValue.getClass().getSimpleName());
                        return fieldValue;
                    }

                    // 递归检查嵌套对象
                    if (fieldValue != null && !isSimpleType(fieldValue.getClass())) {
                        if (fieldValue instanceof Map) {
                            log.trace("extractFromComplexObject() - Recursively checking Map field '{}'", field.getName());
                            Object nestedEntity = extractFromMap((Map<?, ?>) fieldValue);
                            if (nestedEntity != null) {
                                log.debug("extractFromComplexObject() - Found auditable entity in Map field '{}': {}",
                                        field.getName(), nestedEntity.getClass().getSimpleName());
                                return nestedEntity;
                            }
                        } else if (fieldValue instanceof Collection) {
                            log.trace("extractFromComplexObject() - Recursively checking Collection field '{}'", field.getName());
                            Object nestedEntity = extractFromCollection((Collection<?>) fieldValue);
                            if (nestedEntity != null) {
                                log.debug("extractFromComplexObject() - Found auditable entity in Collection field '{}': {}",
                                        field.getName(), nestedEntity.getClass().getSimpleName());
                                return nestedEntity;
                            }
                        } else {
                            log.trace("extractFromComplexObject() - Field '{}' contains complex object but not container type: {}",
                                    field.getName(), fieldValue.getClass().getSimpleName());
                        }
                    }
                } catch (IllegalAccessException e) {
                    log.warn("extractFromComplexObject() - Cannot access field '{}' in class {}: {}",
                            field.getName(), parameterClass.getSimpleName(), e.getMessage());
                } catch (Exception e) {
                    log.warn("extractFromComplexObject() - Error processing field '{}' in class {}: {}",
                            field.getName(), parameterClass.getSimpleName(), e.getMessage());
                }
            }

            long processingTime = System.currentTimeMillis() - startTime;
            log.debug("extractFromComplexObject() - Completed analysis of {} fields in {} ms",
                    fieldIndex, processingTime);

        } catch (SecurityException e) {
            log.warn("extractFromComplexObject() - Security exception accessing fields of class {}: {}",
                    parameterClass.getSimpleName(), e.getMessage());
        } catch (Exception e) {
            log.warn("extractFromComplexObject() - Unexpected error extracting entity from complex object {}: {}",
                    parameterClass.getSimpleName(), e.getMessage());
        }

        log.debug("extractFromComplexObject() - Exit: No auditable entity found in complex object of type {}",
                parameterClass.getSimpleName());
        return null;
    }

    /**
     * 检查对象是否为可审计的实体
     */
    private boolean isAuditableEntity(@Nullable Object obj) {
        if (obj == null) {
            return false;
        }

        return AnnotationUtils.findAnnotation(obj.getClass(), AuditEntity.class) != null;
    }

    /**
     * 检查类型是否为简单类型（不需要进一步检查的类型）
     */
    private boolean isSimpleType(Class<?> clazz) {
        return clazz.isPrimitive() ||
                clazz.equals(String.class) ||
                clazz.equals(Boolean.class) ||
                clazz.equals(Byte.class) ||
                clazz.equals(Short.class) ||
                clazz.equals(Integer.class) ||
                clazz.equals(Long.class) ||
                clazz.equals(Float.class) ||
                clazz.equals(Double.class) ||
                clazz.equals(Character.class) ||
                Number.class.isAssignableFrom(clazz) ||
                clazz.getPackage() != null && clazz.getPackage().getName().startsWith("java.");
    }
}
