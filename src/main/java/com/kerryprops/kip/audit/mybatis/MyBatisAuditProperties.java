package com.kerryprops.kip.audit.mybatis;

import jakarta.annotation.PostConstruct;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.lang.Nullable;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * MyBatis审计配置属性
 *
 * <AUTHOR>
 */
@Slf4j
@Getter
@Setter
@ConfigurationProperties("audit.mybatis")
public class MyBatisAuditProperties {

    /**
     * 是否启用MyBatis审计功能
     */
    private boolean enabled = true;

    /**
     * 常见的实体参数键名
     * 用于从Map参数中提取实体对象
     */
    private List<String> commonEntityKeys = Arrays.asList(
            "entity", "record", "param", "data", "model", "object", "item"
    );

    /**
     * 排除的Mapper方法模式
     * 匹配这些模式的方法将不会被审计
     */
    private Set<String> excludedMapperPatterns = new HashSet<>(Arrays.asList(
            "*.selectBy*",
            "*.findBy*",
            "*.countBy*",
            "*.existsBy*",
            "*.queryBy*"
    ));

    /**
     * 排除的Mapper类
     * 这些Mapper类的所有方法都不会被审计
     */
    private Set<String> excludedMapperClasses = new HashSet<>();

    /**
     * 是否启用批量操作审计
     * 批量操作可能影响性能，可以选择性关闭
     */
    private boolean enableBatchAudit = true;

    /**
     * 批量操作审计的最大记录数
     * 超过此数量的批量操作将不会被审计
     */
    private int maxBatchSize = 1000;

    /**
     * 是否启用详细日志
     */
    private boolean enableDetailedLogging = false;

    /**
     * 实体提取超时时间（毫秒）
     * 防止复杂对象提取时间过长
     */
    private long entityExtractionTimeoutMs = 100;

    /**
     * 在属性加载完成后记录所有配置值
     */
    @PostConstruct
    public void logProperties() {
        log.debug("==== MyBatis Audit Properties Configuration ====");
        log.debug("enabled: {}", enabled);
        log.debug("commonEntityKeys: {}", commonEntityKeys);
        log.debug("excludedMapperPatterns: {}", excludedMapperPatterns);
        log.debug("excludedMapperClasses: {}", excludedMapperClasses);
        log.debug("enableBatchAudit: {}", enableBatchAudit);
        log.debug("maxBatchSize: {}", maxBatchSize);
        log.debug("enableDetailedLogging: {}", enableDetailedLogging);
        log.debug("entityExtractionTimeoutMs: {}", entityExtractionTimeoutMs);
        log.debug("===============================================");
    }

    /**
     * 检查Mapper方法是否应该被排除
     *
     * @param mappedStatementId MyBatis映射语句ID
     * @return 如果应该排除则返回true
     */
    public boolean isMapperMethodExcluded(@Nullable String mappedStatementId) {
        if (mappedStatementId == null) {
            return false;
        }

        // 检查是否匹配排除的类
        for (String excludedClass : excludedMapperClasses) {
            if (mappedStatementId.startsWith(excludedClass)) {
                return true;
            }
        }

        // 检查是否匹配排除的方法模式
        for (String pattern : excludedMapperPatterns) {
            if (matchesPattern(mappedStatementId, pattern)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 简单的模式匹配
     * 支持*通配符
     */
    private boolean matchesPattern(String text, String pattern) {
        if (pattern.equals("*")) {
            return true;
        }

        if (!pattern.contains("*")) {
            return text.equals(pattern);
        }

        // 将模式转换为正则表达式
        String regex = pattern.replace("*", ".*");
        return text.matches(regex);
    }
}
