package com.kerryprops.kip.audit.mybatis;

import com.kerryprops.kip.audit.AuditService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.*;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.lang.Nullable;
import com.kerryprops.kip.audit.AuditEntity;

import java.util.Properties;

/**
 * MyBatis审计拦截器
 * 拦截INSERT、UPDATE、DELETE操作，集成现有审计框架
 * 
 * <AUTHOR> Zhang
 */
@Slf4j
@RequiredArgsConstructor
@Intercepts({
    @Signature(
        type = Executor.class,
        method = "update",
        args = {MappedStatement.class, Object.class}
    )
})
public class MyBatisAuditInterceptor implements Interceptor {

    private final AuditService auditService;
    private final MyBatisEntityExtractor entityExtractor;
    private final MyBatisAuditProperties mybatisAuditProperties;

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        // 检查是否启用MyBatis审计
        if (!mybatisAuditProperties.isEnabled()) {
            return invocation.proceed();
        }

        Object[] args = invocation.getArgs();
        MappedStatement mappedStatement = (MappedStatement) args[0];
        Object parameter = args[1];

        SqlCommandType sqlCommandType = mappedStatement.getSqlCommandType();

        // 只处理INSERT、UPDATE、DELETE操作
        if (!isAuditableOperation(sqlCommandType)) {
            return invocation.proceed();
        }

        // 检查是否为排除的Mapper方法
        if (mybatisAuditProperties.isMapperMethodExcluded(mappedStatement.getId())) {
            log.debug("Mapper method {} is excluded from audit", mappedStatement.getId());
            return invocation.proceed();
        }

        log.debug("MyBatis audit interceptor processing {} operation for statement: {}", 
                 sqlCommandType, mappedStatement.getId());

        try {
            return processAuditableOperation(invocation, mappedStatement, parameter, sqlCommandType);
        } catch (Exception e) {
            log.error("Error in MyBatis audit interceptor for operation {}: ", sqlCommandType, e);
            // 审计失败不应影响业务操作
            return invocation.proceed();
        }
    }

    /**
     * 处理可审计的操作
     */
    private Object processAuditableOperation(Invocation invocation, MappedStatement mappedStatement,
                                           @Nullable Object parameter, SqlCommandType sqlCommandType) throws Throwable {

        // 提取实体对象
        Object entity = entityExtractor.extractEntity(parameter, mappedStatement);
        
        if (!isAuditableEntity(entity)) {
            log.debug("Entity is null or not auditable, skipping audit for statement: {}", 
                     mappedStatement.getId());
            return invocation.proceed();
        }

        log.debug("Processing audit for entity: {} with operation: {}", 
                 entity.getClass().getSimpleName(), sqlCommandType);

        return switch (sqlCommandType) {
            case INSERT -> handleInsert(invocation, entity);
            case UPDATE -> handleUpdate(invocation, entity);
            case DELETE -> handleDelete(invocation, entity);
            default -> invocation.proceed();
        };
    }

    /**
     * 处理INSERT操作
     */
    private Object handleInsert(Invocation invocation, Object entity) throws Throwable {
        Object result = invocation.proceed();
        
        // INSERT后记录创建审计
        auditService.recordCreation(entity);
        log.debug("Recorded creation audit for entity: {}", entity.getClass().getSimpleName());
        
        return result;
    }

    /**
     * 处理UPDATE操作
     */
    private Object handleUpdate(Invocation invocation, Object entity) throws Throwable {
        // UPDATE前缓存原始状态
        auditService.beforeUpdate(entity);
        log.debug("Cached original state for entity: {}", entity.getClass().getSimpleName());
        
        Object result = invocation.proceed();
        
        // UPDATE后记录更新审计
        auditService.recordUpdate(entity);
        log.debug("Recorded update audit for entity: {}", entity.getClass().getSimpleName());
        
        return result;
    }

    /**
     * 处理DELETE操作
     */
    private Object handleDelete(Invocation invocation, Object entity) throws Throwable {
        // DELETE前记录删除审计
        auditService.recordDeletion(entity);
        log.debug("Recorded deletion audit for entity: {}", entity.getClass().getSimpleName());

        return invocation.proceed();
    }

    /**
     * 检查操作是否需要审计
     */
    private boolean isAuditableOperation(SqlCommandType sqlCommandType) {
        return sqlCommandType == SqlCommandType.INSERT ||
               sqlCommandType == SqlCommandType.UPDATE ||
               sqlCommandType == SqlCommandType.DELETE;
    }

    /**
     * 检查实体是否可审计
     * 只有标注@AuditEntity注解的实体才会被审计
     */
    private boolean isAuditableEntity(@Nullable Object entity) {
        if (entity == null) {
            return false;
        }
        
        boolean isAuditable = AnnotationUtils.findAnnotation(entity.getClass(), AuditEntity.class) != null;
        log.debug("Entity {} is auditable: {}", entity.getClass().getSimpleName(), isAuditable);
        
        return isAuditable;
    }

    @Override
    public Object plugin(Object target) {
        // 只对Executor进行代理
        if (target instanceof Executor) {
            return Plugin.wrap(target, this);
        }
        return target;
    }

    @Override
    public void setProperties(Properties properties) {
        // 可以从properties中读取配置，当前使用Spring配置
        log.debug("MyBatis audit interceptor properties: {}", properties);
    }
}
