package com.kerryprops.kip.audit;

/**
 * Exception thrown during audit entity post-processing.
 * This exception is thrown when errors occur during audit entity scanning
 * or listener registration processes.
 *
 * <AUTHOR>
 */
public class AuditEntityPostProcessorException extends RuntimeException {

    /**
     * Constructs a new exception with the specified detail message and cause.
     *
     * @param message the detail message
     * @param cause the cause of the exception
     */
    public AuditEntityPostProcessorException(final String message,
                                             final Throwable cause) {
        super(message, cause);
    }
}