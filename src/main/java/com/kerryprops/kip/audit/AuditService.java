package com.kerryprops.kip.audit;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;


/**
 * Service for recording audit events for entity lifecycle operations.
 * This service handles creation, update, and deletion audit events,
 * managing entity state caching and change detection.
 */
@Slf4j
@RequiredArgsConstructor
public final class AuditService {



    /** Change detector for identifying field-level changes. */
    private final ChangeDetector changeDetector;

    /** Factory for creating audit event requests. */
    private final AuditEventFactory auditEventFactory;

    /** Queue for processing audit events asynchronously. */
    private final AuditQueue auditQueue;

    /**
     * Called before entity update to cache the original entity state.
     *
     * Note: For MyBatis-only implementation, original state caching is handled
     * by the MyBatis interceptor.
     * @param entity the entity to be updated
     */
    public void beforeUpdate(final Object entity) {
        log.debug("MyBatis audit: beforeUpdate called for entity: {}",
                entity.getClass().getSimpleName());
    }

    /**
     * Records entity creation audit event.
     *
     * @param entity the created entity
     */
    public void recordCreation(final Object entity) {
        try {
            var auditLog = auditEventFactory.createAuditEventRequest(entity,
                    AuditOperation.CREATE);
            var fieldChanges = changeDetector.detectChanges(null, entity);
            auditLog.setFieldChanges(fieldChanges);
            auditQueue.enqueue(auditLog);
        } catch (Exception e) {
            log.error("Error recording entity creation: ", e);
        }
    }

    /**
     * Records entity update audit event.
     *
     * @param entity the updated entity
     */
    public void recordUpdate(final Object entity) {
        try {
            Object oldEntity = fetchOriginalEntityWithFallback(entity);
            var auditLog = auditEventFactory.createAuditEventRequest(entity,
                    AuditOperation.UPDATE);
            var fieldChanges = changeDetector.detectChanges(oldEntity, entity);
            if (fieldChanges.isEmpty()) {
                log.warn("No field changes detected for entity: {}", entity);
                return; // Skip audit logging if no field changes
            }
            auditLog.setFieldChanges(fieldChanges);
            auditQueue.enqueue(auditLog);
        } catch (Exception e) {
            log.error("Error recording entity update: ", e);
        }
    }

    /**
     * For MyBatis-only implementation, original entity state is handled
     * by the MyBatis interceptor. This method returns the current entity
     * as the original state is not cached separately.
     *
     * @param entity current entity
     * @return the current entity (MyBatis handles original state separately)
     */
    private Object fetchOriginalEntityWithFallback(final Object entity) {
        log.debug("MyBatis audit: using current entity state for comparison");
        return entity;
    }

    /**
     * Records entity deletion audit event.
     *
     * @param entity the deleted entity
     */
    public void recordDeletion(final Object entity) {
        try {
            AuditEventRequest auditLog = auditEventFactory
                    .createAuditEventRequest(entity, AuditOperation.DELETE);
            var fieldChanges = changeDetector.detectChanges(entity, null);
            auditLog.setFieldChanges(fieldChanges);
            auditQueue.enqueue(auditLog);
        } catch (Exception e) {
            log.error("Error recording entity deletion: ", e);
        }
    }

}