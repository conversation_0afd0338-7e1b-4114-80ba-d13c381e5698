package com.kerryprops.kip.audit;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Audit field control annotation.
 * Provides field-level audit control, including alias settings and
 * include/exclude logic.
 *
 * <AUTHOR> Zhang
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface AuditField {

    /**
     * Display alias for the field.
     * If not specified, the camelCase conversion of the field name will be
     * used as the display name.
     *
     * @return field alias
     */
    String alias() default "";

    /**
     * Whether to include this field for auditing.
     * When set to false, the field will not be audited (unless forced to be
     * excluded by exclude).
     *
     * @return whether to include
     */
    boolean include() default true;

    /**
     * Whether to exclude this field from auditing.
     * When set to true, the field will be forcibly excluded with the highest
     * priority.
     *
     * @return whether to exclude
     */
    boolean exclude() default false;
}