package com.kerryprops.kip.audit;

import lombok.RequiredArgsConstructor;
import org.springframework.web.client.RestTemplate;

import java.util.List;

/**
 * Client for sending audit events to external audit service.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> 2025-07-01 17:41:32
 */
@RequiredArgsConstructor
public final class AuditClient {

    /** REST template for HTTP communication. */
    private final RestTemplate restTemplate;

    /** Audit configuration properties. */
    private final AuditProperties auditProperties;

    /**
     * Sends audit event requests to the configured audit service.
     *
     * @param requests list of audit event requests to send
     */
    public void send(final List<AuditEventRequest> requests) {
        restTemplate.postForEntity(auditProperties.getUrl(), requests,
                Void.class);
    }

}
