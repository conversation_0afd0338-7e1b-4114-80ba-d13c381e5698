package com.kerryprops.kip.audit;

/**
 * Exception thrown when a requested resource is not found.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> 2025-07-02 15:20:45
 */
public class NotFoundException extends RuntimeException {

    /**
     * Constructs a new NotFoundException with the specified detail message.
     *
     * @param message the detail message
     */
    public NotFoundException(final String message) {
        super(message);
    }
}
