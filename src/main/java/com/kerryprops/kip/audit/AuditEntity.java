package com.kerryprops.kip.audit;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 审计实体控制注解.
 * 提供类级别的审计控制，支持白名单/黑名单模式以及默认策略设置。
 *
 * <AUTHOR> Zhang
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface AuditEntity {

    /**
     * 仅审计指定的字段（白名单模式）.
     * 当指定了includeFields时，只有列表中的字段会被审计，
     * 除非字段被exclude注解或全局配置强制排除。
     *
     * @return 包含的字段名数组
     */
    String[] includeFields() default {};

    /**
     * 排除指定的字段（黑名单模式）.
     * 列表中的字段将不会被审计，但优先级低于全局配置和字段级exclude注解。
     *
     * @return 排除的字段名数组
     */
    String[] excludeFields() default {};

    /**
     * 默认是否包含所有字段进行审计.
     * 当没有明确的includeFields指定时，此设置决定未标注字段的默认行为。
     *
     * @return 默认包含策略
     */
    boolean defaultInclude() default true;

    /**
     * 实体描述（可选）.
     *
     * @return 实体描述
     */
    String description() default "";
}
