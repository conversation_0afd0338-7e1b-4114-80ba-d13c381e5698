package com.kerryprops.kip.audit;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

import java.util.Optional;

/**
 * Thread-local context for storing audit information during request processing.
 * This class manages user context extracted from HTTP headers and provides
 * it to the audit system for tracking changes.
 *
 * <AUTHOR> Zhang
 */
@Slf4j
public final class AuditContext {

    /**
     * Thread-local storage for user context information.
     */
    private static final ThreadLocal<XuserInfo> USER_CONTEXT =
            new ThreadLocal<>();

    /**
     * Thread-local storage for audit request information.
     */
    private static final ThreadLocal<AuditRequestInfo> AUDIT_INFO_STORAGE =
            new ThreadLocal<>();

    /**
     * Private constructor to prevent instantiation of utility class.
     */
    private AuditContext() {
        // Utility class
    }

    /**
     * Gets the current user information from thread-local context.
     *
     * @return current user information or anonymous user if none set
     */
    public static XuserInfo getCurrentUser() {
        return Optional.ofNullable(USER_CONTEXT.get())
                .orElse(XuserInfo.ANONYMOUS_USER);
    }

    /**
     * Stores user information in thread-local context.
     *
     * @param userInfo the user information to store
     */
    public static void storeCurrentUser(final XuserInfo userInfo) {
        Assert.notNull(userInfo, "User information cannot be null");
        USER_CONTEXT.set(userInfo);
        log.info("Set Audit user information for user name: {}",
                userInfo.getNickName());
    }

    /**
     * Gets the current user's nickname.
     *
     * @return current user's nickname
     */
    public static String getCurrentUserNickname() {
        return getCurrentUser().getNickName();
    }

    /**
     * Gets the current user's ID.
     *
     * @return current user's ID
     */
    public static Long getCurrentUserId() {
        return getCurrentUser().getUserId();
    }

    /**
     * Stores audit request information in thread-local context.
     *
     * @param auditRequestInfo the audit request information to store
     */
    public static void storeAuditRequestInfo(
            final AuditRequestInfo auditRequestInfo) {
        Assert.notNull(auditRequestInfo, "AuditRequestInfo cannot be null");
        AUDIT_INFO_STORAGE.set(auditRequestInfo);
    }

    /**
     * Gets the current audit request information.
     *
     * @return current audit request information
     */
    public static AuditRequestInfo getAuditRequestInfo() {
        return AUDIT_INFO_STORAGE.get();
    }

    /**
     * Clears all thread-local context information.
     */
    public static void clear() {
        USER_CONTEXT.remove();
        AUDIT_INFO_STORAGE.remove();
        log.info("Cleared audit context for current thread");
    }

}
