package com.kerryprops.kip.audit;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;

import jakarta.annotation.PostConstruct;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * Configuration properties for the audit system.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Zhang 2025-07-01 17:49:35
 */
@Slf4j
@Getter
@Setter
@ConfigurationProperties("audit")
public class AuditProperties {

    /** Default maximum field length for audit data. */
    private static final int DEFAULT_MAX_FIELD_LENGTH = 1000;

    /** Default fixed delay for scheduled tasks in milliseconds. */
    private static final long DEFAULT_FIXED_DELAY = 1000L;

    /** Maximum field length for audit data. */
    private int maxFieldLength = DEFAULT_MAX_FIELD_LENGTH;

    /** HTTP header configuration. */
    private Header header = new Header();

    /** URL for the audit service endpoint. */
    private String url = "http://toolkit-service/api/audit/events/batch";

    /** Set of field names to exclude from auditing. */
    private Set<String> excludedFields = new HashSet<>();

    /**
     * Global field alias mapping configuration.
     * Key: field name, Value: display alias
     */
    private Map<String, String> fieldAliases = new HashMap<>();

    /**
     * Global default include strategy.
     * Default behavior when entity doesn't use @AuditEntity annotation
     * or doesn't specify defaultInclude.
     */
    private boolean defaultInclude = true;

    /** Service identifier for audit events. */
    @Value("${audit.service-id:${spring.application.name}}")
    private String serviceId;

    /** Cron expression for scheduled audit processing. */
    private String cron = "* * * * * ?";

    /** Fixed delay for scheduled audit processing in milliseconds. */
    private long fixedDelay = DEFAULT_FIXED_DELAY;

    /** Queue configuration for audit event processing. */
    private Queue queue = new Queue();

    /** HTTP client configuration for audit service communication. */
    private HttpClient httpClient = new HttpClient();

    /**
     * MyBatis audit functionality configuration.
     */
    private Mybatis mybatis = new Mybatis();

    /**
     * Logs all configuration values after properties are loaded.
     */
    @PostConstruct
    public void logProperties() {
        log.debug("==== Audit Properties Configuration ====");
        log.debug("maxFieldLength: {}", maxFieldLength);
        log.debug("url: {}", url);
        log.debug("serviceId: {}", serviceId);
        log.debug("cron: {}", cron);
        log.debug("fixedDelay: {}", fixedDelay);
        log.debug("excludedFields: {}", excludedFields);
        log.debug("fieldAliases: {}", fieldAliases);
        log.debug("defaultInclude: {}", defaultInclude);
        log.debug("mybatis: {}", mybatis);
        log.debug("header: {}", header);
        log.debug("queue: {}", queue);
        log.debug("httpClient: {}", httpClient);
        log.debug("========================================");
    }

    /**
     * Queue configuration for audit event processing.
     */
    @Getter
    @Setter
    @ToString
    public static class Queue {
        /** Default queue capacity. */
        private static final int DEFAULT_CAPACITY = 10000;

        /** Default batch size for processing. */
        private static final int DEFAULT_BATCH_SIZE = 100;

        /** Maximum queue capacity. */
        private int capacity = DEFAULT_CAPACITY;

        /** Batch size for processing audit events. */
        private int batchSize = DEFAULT_BATCH_SIZE;
    }

    /**
     * HTTP client configuration for audit service communication.
     */
    @Getter
    @Setter
    @ToString
    public static class HttpClient {
        /** Default connection timeout in milliseconds. */
        private static final int DEFAULT_CONNECT_TIMEOUT = 5000;

        /** Default read timeout in milliseconds. */
        private static final int DEFAULT_READ_TIMEOUT = 10000;

        /** Connection timeout in milliseconds. */
        private int connectTimeout = DEFAULT_CONNECT_TIMEOUT;

        /** Read timeout in milliseconds. */
        private int readTimeout = DEFAULT_READ_TIMEOUT;
    }

    /**
     * HTTP header configuration for extracting audit context.
     */
    @Getter
    @Setter
    @ToString
    public static class Header {

        /** Header name for user information. */
        private String user = "X-User";

        /** Header name for UI model information. */
        private String uiModel = "X-UI-Model";

        /** Header name for conversation ID. */
        private String conversationId = "X-Conversation-Id";

        /** Header name for correlation ID. */
        private String correlationId = "X-Correlation-ID";

        /**
         * Header name for audit filter key.
         */
        private String auditFilterKey = "X-Audit-Filter-Key";

        /**
         * Header name for audit filter value.
         */
        private String auditFilterValue = "X-Audit-Filter-Value";

    }

    /**
     * MyBatis audit functionality configuration.
     */
    @Getter
    @Setter
    @ToString
    public static class Mybatis {
        /**
         * Whether to enable MyBatis audit functionality.
         */
        private boolean enabled = true;
    }

}
