package com.kerryprops.kip.audit;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/***********************************************************************************************************************
 * Project - gateway-service
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - Bert
 * Created Date - 06/30/2021 18:14
 **********************************************************************************************************************/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class XuserInfo implements Serializable {
    public static final XuserInfo ANONYMOUS_USER = XuserInfo.builder()
            .userId(-1L)
            .nickName("anonymous")
            .build();

    private Long userId;

    private String nickName;
}
