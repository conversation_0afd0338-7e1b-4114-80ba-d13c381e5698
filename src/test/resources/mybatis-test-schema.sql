-- MyBatis Test Database Schema
-- This schema is specifically for MyBatis integration tests

-- Drop tables if they exist
DROP TABLE IF EXISTS test_products;

-- Create test_products table for MyBatis tests
CREATE TABLE test_products (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'ACTIVE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    internal_version INTEGER DEFAULT 1,
    created_by BIGINT,
    updated_by BIGINT
);

-- Insert some test data
INSERT INTO test_products (name, description, price, status, created_by, updated_by) VALUES
('Test Product 1', 'A test product for MyBatis testing', 99.99, 'ACTIVE', 1, 1),
('Test Product 2', 'Another test product', 149.99, 'ACTIVE', 1, 1),
('Test Product 3', 'Third test product', 29.99, 'INACTIVE', 1, 1);
