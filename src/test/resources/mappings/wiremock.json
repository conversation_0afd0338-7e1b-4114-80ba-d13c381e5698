{"mappings": [{"request": {"method": "POST", "urlPath": "/coupon-management/coupon/redeem-after-lock"}, "response": {"status": 200, "body": "[\"23zosbwp6cl_t\"]"}}, {"request": {"method": "POST", "urlPath": "/coupon-management/coupon/lock"}, "response": {"status": 200, "body": "[\"23zosbwp6cl_t\"]"}}, {"request": {"method": "GET", "urlPath": "/8a8880b183e8f1c4018407a6d5a30000/promotion/activity", "queryParameters": {"notParticipateDiscountAmount": {"equalTo": "0"}, "consumptionAmount": {"equalTo": "111"}, "kipUserId": {"equalTo": "8a848706859e9e270186154feac90069"}}}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "jsonBody": {"activityId": "", "reductionAmount": 0, "discountAmount": 111, "message": ""}}}, {"request": {"method": "GET", "urlPattern": "/coupon-management/coupons/.*"}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "jsonBody": [{"exchangeLbsId": "8a8883557cca9463017ccb002b360001", "exchangeLbsName": "杭州嘉里中心", "belongLbsId": "8a8883557cca9463017ccb002b360001", "brandId": "8a8884e77cc9e70a017cca1011970001", "exchangeRecordId": "f315fc326e9f89f418cb96492b8872a5", "couponId": "2327d6555d569bcf5e90fc0c3aa8fa4f", "payMethod": "1", "couponName": "DO-CS-19.99超值套餐", "couponCode": "2qedkfis6oc_t", "couponBatchId": "2c07fcff1ef39422e80d1fbf1998943a", "couponBatchName": "DO-CS-19.99超值团购", "issueState": "2", "vipCode": "KERRY100383351", "mobile": "13207981378", "exchangePayPoints": 0, "exchangePayAmount": 0.0, "exchangeDate": "2024-12-30 17:52:37", "exchangeOperator": null, "exchangeSource": "benefit-center", "exchangeSourceRemark": "", "creator": "<PERSON><PERSON>", "wxOrderId": "", "redeemState": "0", "redeemStateName": null, "redeemOperator": "", "redeemDate": null, "expireDate": "2025-01-31 23:59:59", "redeemLbsId": "", "redeemLbsName": "", "redeemChannels": "discount", "limitRedeemShops": "0", "couponType": "3", "limitWeekday": "1", "availableWeekdays": "", "stackType": "3", "redeemLimitionPerOrder": 999, "redeemStartDate": "2024-12-30 17:52:11", "redeemEndDate": "2025-01-31 23:59:59", "activityId": null, "activityPrecondition": "0", "useRequirement": "0", "equivalentValue": "19.99", "parkingType": "1", "parkingDeductHour": null, "couponState": "1", "couponStateName": null, "lockState": 1, "parentExchangeRecordId": "", "maxDeductAmount": null, "discount": null, "consumptionRequired": null, "couponThumbnailPictureUrl": "https://static-le.kerryprops.com.cn/crm/coupon_activity/coupon/73239e6d974349bd9097a2444e97b58f.jfif", "redeemFlag": "1", "memberGrade": "3", "state": null, "refundStatus": "0"}]}}, {"request": {"method": "POST", "url": "/activity/pre-reduction"}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "jsonBody": {"code": 0, "companyCode": "31017", "discountAmount": 90, "tenantId": "TENANT123", "message": "Operation successful", "messageCn": "操作成功"}}}, {"request": {"method": "GET", "url": "/ums/contracting/find_by_company_code?companyCode=31017"}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "body": "{\"id\":6,\"tenantId\":\"\",\"companyCode\":\"31017\",\"accountNo\":\"dhjt0000000000D\",\"lbsName\":\"杭州嘉里中心办公楼\",\"lbsId\":\"4028e3817bf860f3017bf86279b50001\",\"brandId\":\"4028e3817c2b3f79017c2b48c54c0000\",\"accountName\":\"\",\"state\":\"SUCCESS\",\"failReason\":\"\",\"originStatus\":\"\",\"rawResult\":\"\",\"createdDate\":\"2024-05-11T17:50:54+08:00\",\"lastModifiedDate\":\"2024-05-13T17:27:07+08:00\"}"}}, {"request": {"method": "POST", "url": "/sessions"}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "jsonBody": {"body": {"sessionInfo": {"sessionId": "baa3f886-f86b-4f14-8157-c51a27cbe9b0", "createdDate": "2024-09-20T14:12:28.000Z"}}}}}, {"request": {"method": "POST", "urlPattern": "/activity/recover/.*"}, "response": {"status": 200, "headers": {"Content-Type": "text/plain"}, "body": "accepted"}}, {"request": {"method": "POST", "urlPattern": "/activity/decrease/.*"}, "response": {"status": 200, "headers": {"Content-Type": "text/plain"}, "body": "accepted"}}, {"request": {"method": "POST", "url": "/refund"}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "jsonBody": {"body": {"error": "账户余额不足, 退款失败"}}}}, {"request": {"method": "GET", "url": "/brand_guide/2"}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "jsonBody": [{"id": "1", "businessHours": "h3QRu1Ma", "logoPic": "http://www.baidu.com", "phone": "11111", "shopLocation": "7j<PERSON><PERSON>"}]}}, {"request": {"method": "GET", "url": "/brand_guide/1"}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "jsonBody": [{"id": "1", "businessHours": "h3QRu1Ma", "logoPic": "http://www.baidu.com", "phone": "11111", "shopLocation": "7j<PERSON><PERSON>"}]}}, {"request": {"method": "GET", "url": "/tenant/lbs/addTenantConfig-lbs-1?lbsId=addTenantConfig-lbs-1&crmOperateStatus=true"}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "jsonBody": [{"id": "1", "crmOperateStatus": true}]}}, {"request": {"method": "GET", "url": "/brand_guide/1450371719088406604,1450371719088406598"}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "jsonBody": [{"guideId": "test_33e73a33f88b", "businessHours": "test_1d0c39b55c6f", "logoPic": "test_c8ea5c3a64c4", "phone": "test_acc3824a4d26", "shopLocation": "test_cba5f1a700f4"}]}}, {"request": {"method": "GET", "url": "/tenant/lbs/8aaa81cb7c836c6b017c83e2c76f0000?lbsId=8aaa81cb7c836c6b017c83e2c76f0000&crmOperateStatus=true"}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "jsonBody": [{"id": "8aaa81838167a5a301816a46692b000f", "crmOperateStatus": true}]}}, {"request": {"method": "GET", "url": "/tenant/lbs/8aaa81cb7c836c6b017c83e2c76f0000?lbsId=8aaa81cb7c836c6b017c83e2c76f0000&crmOperateStatus=true"}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "body": "[{\"id\":\"8aaa81838167a5a301816a46692b000f\",\"crmOperateStatus\":true}]"}}, {"request": {"method": "GET", "urlPattern": "/member/kip_user_id/.*"}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "body": "{\"vipcode\":\"vip-1\"}"}}, {"request": {"method": "POST", "urlPath": "/api/audit/events/batch"}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "jsonBody": {"success": true, "message": "Audit events processed successfully"}}}, {"request": {"method": "GET", "urlPattern": "/tenant/lbs/.*"}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "body": "[{\"id\":\"2c9d85e1864d5ed201864e1192c90000\",\"name\":\"\",\"brandName\":\"测试的所得税\",\"status\":\"ENABLE\",\"note\":\"\",\"contractNo\":\"HKC00021027\",\"shopName\":\"test\",\"retailBrandId\":\"1447854345579749378\",\"retailBrandName\":\"testceshi测试\",\"firstFormatCode\":\"01\",\"firstFormatName\":\"购物\"},{\"id\":\"8aaa81a27f8cb15d017f8cbd0dd90000\",\"name\":\"杭州百美汇影院有限公司,上海香珍食品有限公司杭州庆春路分公司\",\"brandName\":\"香珍食品\",\"status\":\"ENABLE\",\"note\":\"\",\"contractNo\":\"HKC00041001\",\"shopName\":\"餐饮1\",\"retailBrandId\":\"1448187541341081602\",\"retailBrandName\":\"旧元素餐饮长沙重庆音乐快乐\",\"firstFormatCode\":\"02\",\"firstFormatName\":\"餐饮\"},{\"id\":\"8aaa81a27f8cb15d017f8ccd916a0003\",\"name\":\"\",\"brandName\":\"杭州鑫晨服务\",\"status\":\"ENABLE\",\"note\":\"\",\"contractNo\":\"HKC00041002\",\"shopName\":\"汽车\",\"retailBrandId\":\"1503627163211112450\",\"retailBrandName\":\"汽车\",\"firstFormatCode\":\"4\",\"firstFormatName\":\"汽车消费\"},{\"id\":\"8aaa81a27f8cb15d017f917bd7af000a\",\"name\":\"尚乐（杭州）餐饮管理有限公司\",\"brandName\":\"test\",\"status\":\"ENABLE\",\"note\":\"\",\"contractNo\":\"HKC00041004\",\"shopName\":\"admin12\",\"retailBrandId\":\"1550411182514610177\",\"retailBrandName\":\"十二饭店1\",\"firstFormatCode\":\"02\",\"firstFormatName\":\"餐饮\"},{\"id\":\"8aaa81e281fb3f030181fb417b970000\",\"name\":\"\",\"brandName\":\"项旺摆\",\"status\":\"ENABLE\",\"note\":\"\",\"contractNo\":\"HKC00041003\",\"shopName\":\"项旺摆\",\"retailBrandId\":\"1447854345579749378\",\"retailBrandName\":\"testceshi测试\",\"firstFormatCode\":\"01\",\"firstFormatName\":\"购物\"},{\"id\":\"8aaa81e281fb3f030181fb44720a0002\",\"name\":\"新元素餐饮管理（上海）有限公司杭州第二分公司\",\"brandName\":\"新元素餐饮管理（上海）有限公司杭州第二分公司\",\"status\":\"ENABLE\",\"note\":\"\",\"contractNo\":\"HKC00041006\",\"shopName\":\"新元素餐饮\",\"retailBrandId\":\"1447854345579749378\",\"retailBrandName\":\"testceshi测试\",\"firstFormatCode\":\"01\",\"firstFormatName\":\"购物\"},{\"id\":\"8aaa82017ee81259017ef5fc16830002\",\"name\":\"\",\"brandName\":\"test001\",\"status\":\"ENABLE\",\"note\":\"\",\"contractNo\":\"HKC00021028\",\"shopName\":\"ts\",\"retailBrandId\":\"1592063886227660808\",\"retailBrandName\":\"绝味鸭脖1\",\"firstFormatCode\":\"02\",\"firstFormatName\":\"餐饮\"},{\"id\":\"8aaa82257cb6de27017cb6e723aa0000\",\"name\":\"新元素餐饮管理（上海）有限公司杭州第二分公司\",\"brandName\":\"测试租户V1\",\"status\":\"ENABLE\",\"note\":\"\",\"contractNo\":\"HKC00041005\",\"shopName\":\"新元素餐饮管理（上海）有限公司杭州第二分公司\",\"retailBrandId\":\"1447854345579749378\",\"retailBrandName\":\"testceshi测试\",\"firstFormatCode\":\"01\",\"firstFormatName\":\"购物\"},{\"id\":\"8aaa825a85153216018518d8cac70002\",\"name\":\"绫致时装（天津）有限公司\",\"brandName\":\" 绫致时装（杭州）有限公司\",\"status\":\"ENABLE\",\"note\":\"\",\"contractNo\":\"HKC00021025\",\"shopName\":\"绫致时装（杭州）有限公司\",\"retailBrandId\":\"1592063886227661685\",\"retailBrandName\":\"绫致时装（杭州）有限公司\",\"firstFormatCode\":\"1\",\"firstFormatName\":\"服装\"},{\"id\":\"8aaa825a85153216018518dbf0980004\",\"name\":\"深圳回收宝科技有限公司\",\"brandName\":\"杭州回收宝科技有限公司\",\"status\":\"ENABLE\",\"note\":\"\",\"contractNo\":\"HKC00021024\",\"shopName\":\"杭州回收宝科技有限公司12321\",\"retailBrandId\":\"1592063886227661686\",\"retailBrandName\":\"杭州回收宝科技有限公司\",\"firstFormatCode\":\"2\",\"firstFormatName\":\"科技18\"},{\"id\":\"8aaa825b7d4abe55017d50f8274f0002\",\"name\":\"\",\"brandName\":\"测试公司1\",\"status\":\"ENABLE\",\"note\":\"\",\"contractNo\":\"HKC00021011\",\"shopName\":\"test\",\"retailBrandId\":\"1447854345579749378\",\"retailBrandName\":\"testceshi测试\",\"firstFormatCode\":\"01\",\"firstFormatName\":\"购物\"},{\"id\":\"8aaa83397bf7310e017bf78c991c0003\",\"name\":\"\",\"brandName\":\"五十七度湘\",\"status\":\"ENABLE\",\"note\":\"\",\"contractNo\":\"HKC00021029\",\"shopName\":\"57\",\"retailBrandId\":\"1448135049668464642\",\"retailBrandName\":\"巴拉巴拉\",\"firstFormatCode\":\"01\",\"firstFormatName\":\"购物\"},{\"id\":\"8aaa83987bc9c2e8017bc9c6eebb0001\",\"name\":\"新元素餐饮管理（上海）有限公司杭州第二分公司\",\"brandName\":\"喜茶\",\"status\":\"ENABLE\",\"note\":\"\",\"contractNo\":\"HKC00021030\",\"shopName\":\"222222000022200\",\"retailBrandId\":\"1592063886227660806\",\"retailBrandName\":\"gg\",\"firstFormatCode\":\"01\",\"firstFormatName\":\"购物\"},{\"id\":\"8aaa87007de0328b017de123a7550002\",\"name\":\"\",\"brandName\":\"1234\",\"status\":\"ENABLE\",\"note\":\"\",\"contractNo\":\"HKC00021026\",\"shopName\":\"别名\",\"retailBrandId\":\"1592063886227661705\",\"retailBrandName\":\"otozvj\",\"firstFormatCode\":\"01\",\"firstFormatName\":\"购物\"}]"}}]}