-- 测试产品表（用于MyBatis审计测试）
CREATE TABLE IF NOT EXISTS test_products (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    price DECIMAL(10,2),
    status VARCHAR(20) DEFAULT 'ACTIVE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    internal_version INTEGER DEFAULT 1,
    created_by BIGINT,
    updated_by BIGINT
);

-- 插入测试数据
INSERT INTO test_products (name, description, price, status, created_by, updated_by) VALUES
('笔记本电脑', '高性能笔记本电脑', 5999.99, 'ACTIVE', 1, 1),
('无线鼠标', '蓝牙无线鼠标', 99.99, 'ACTIVE', 1, 1),
('机械键盘', 'RGB机械键盘', 299.99, 'ACTIVE', 1, 1);
