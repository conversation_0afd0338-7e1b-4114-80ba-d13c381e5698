package com.kerryprops.kip.audit.mybatis;

import org.junit.jupiter.api.Test;
import org.springframework.lang.Nullable;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Null Safety 注解验证测试
 * 验证 MyBatis 审计相关类是否正确应用了 @Nullable 注解
 * 
 * <AUTHOR> Zhang
 */
class NullSafetyValidationTest {

    @Test
    void testMyBatisEntityExtractorNullSafety() throws Exception {
        MyBatisEntityExtractor extractor = new MyBatisEntityExtractor(new MyBatisAuditProperties());
        
        // 测试 extractEntity 方法的参数注解
        Method extractEntityMethod = MyBatisEntityExtractor.class.getMethod(
            "extractEntity", Object.class, org.apache.ibatis.mapping.MappedStatement.class);
        
        Parameter[] parameters = extractEntityMethod.getParameters();
        
        // 第一个参数应该有 @Nullable 注解
        assertTrue(parameters[0].isAnnotationPresent(Nullable.class), 
                  "First parameter should have @Nullable annotation");
        
        // 第二个参数应该有 @Nullable 注解
        assertTrue(parameters[1].isAnnotationPresent(Nullable.class), 
                  "Second parameter should have @Nullable annotation");
        
        // 返回值应该有 @Nullable 注解
        assertTrue(extractEntityMethod.isAnnotationPresent(Nullable.class), 
                  "Return type should have @Nullable annotation");
    }

    @Test
    void testMyBatisAuditPropertiesNullSafety() throws Exception {
        MyBatisAuditProperties properties = new MyBatisAuditProperties();
        
        // 测试 isMapperMethodExcluded 方法的参数注解
        Method isExcludedMethod = MyBatisAuditProperties.class.getMethod(
            "isMapperMethodExcluded", String.class);
        
        Parameter[] parameters = isExcludedMethod.getParameters();
        
        // 参数应该有 @Nullable 注解
        assertTrue(parameters[0].isAnnotationPresent(Nullable.class), 
                  "Parameter should have @Nullable annotation");
    }

    @Test
    void testTestProductNullSafety() throws Exception {
        TestProduct product = new TestProduct();
        
        // 验证所有字段都可以为 null（通过设置 null 值测试）
        assertDoesNotThrow(() -> {
            product.setId(null);
            product.setName(null);
            product.setDescription(null);
            product.setPrice(null);
            product.setStatus(null);
            product.setCreatedAt(null);
            product.setUpdatedAt(null);
            product.setInternalVersion(null);
            product.setCreatedBy(null);
            product.setUpdatedBy(null);
        }, "All fields should accept null values");
        
        // 验证 getter 方法返回 null
        assertNull(product.getId());
        assertNull(product.getName());
        assertNull(product.getDescription());
        assertNull(product.getPrice());
        assertNull(product.getStatus());
        assertNull(product.getCreatedAt());
        assertNull(product.getUpdatedAt());
        assertNull(product.getInternalVersion());
        assertNull(product.getCreatedBy());
        assertNull(product.getUpdatedBy());
    }

    @Test
    void testTestProductMapperNullSafety() throws Exception {
        // 验证 Mapper 方法可以接受 null 参数
        // 注意：这里只是验证方法签名，不实际调用数据库操作
        
        Method deleteProductMethod = TestProductMapper.class.getMethod("deleteProduct", Long.class);
        Parameter[] deleteParams = deleteProductMethod.getParameters();
        assertTrue(deleteParams[0].isAnnotationPresent(Nullable.class), 
                  "deleteProduct parameter should have @Nullable annotation");
        
        Method selectByIdMethod = TestProductMapper.class.getMethod("selectById", Long.class);
        Parameter[] selectParams = selectByIdMethod.getParameters();
        assertTrue(selectParams[0].isAnnotationPresent(Nullable.class), 
                  "selectById parameter should have @Nullable annotation");
        assertTrue(selectByIdMethod.isAnnotationPresent(Nullable.class), 
                  "selectById return type should have @Nullable annotation");
        
        Method deleteByStatusMethod = TestProductMapper.class.getMethod("deleteByStatus", String.class);
        Parameter[] deleteByStatusParams = deleteByStatusMethod.getParameters();
        assertTrue(deleteByStatusParams[0].isAnnotationPresent(Nullable.class), 
                  "deleteByStatus parameter should have @Nullable annotation");
    }

    @Test
    void testPackageInfoExists() {
        // 验证 package-info.java 文件存在并且包含正确的注解
        Package mybatisPackage = MyBatisAuditInterceptor.class.getPackage();
        assertNotNull(mybatisPackage, "MyBatis audit package should exist");
        
        // 验证包级别的注解（这些注解会在运行时生效）
        assertTrue(mybatisPackage.getName().equals("com.kerryprops.kip.audit.mybatis"), 
                  "Package name should be correct");
    }

    @Test
    void testNullSafetyInPractice() {
        // 实际测试 null safety 在运行时的行为
        MyBatisAuditProperties properties = new MyBatisAuditProperties();
        MyBatisEntityExtractor extractor = new MyBatisEntityExtractor(properties);
        
        // 测试传入 null 参数不会抛出异常
        assertDoesNotThrow(() -> {
            Object result = extractor.extractEntity(null, null);
            assertNull(result, "Should return null for null parameters");
        }, "Should handle null parameters gracefully");
        
        // 测试 properties 的 null 处理
        assertDoesNotThrow(() -> {
            boolean result = properties.isMapperMethodExcluded(null);
            assertFalse(result, "Should return false for null mapper method ID");
        }, "Should handle null mapper method ID gracefully");
    }

    @Test
    void testEntityFieldsNullability() {
        // 测试实体字段的可空性
        TestProduct product = new TestProduct();
        
        // 测试设置各种类型的 null 值
        product.setId(null);                          // Long
        product.setName(null);                        // String
        product.setDescription(null);                 // String
        product.setPrice(null);                       // BigDecimal
        product.setStatus(null);                      // String
        product.setCreatedAt(null);                   // LocalDateTime
        product.setUpdatedAt(null);                   // LocalDateTime
        product.setInternalVersion(null);             // Integer
        product.setCreatedBy(null);                   // Long
        product.setUpdatedBy(null);                   // Long
        
        // 验证所有字段都正确设置为 null
        assertAll("All fields should be nullable",
            () -> assertNull(product.getId()),
            () -> assertNull(product.getName()),
            () -> assertNull(product.getDescription()),
            () -> assertNull(product.getPrice()),
            () -> assertNull(product.getStatus()),
            () -> assertNull(product.getCreatedAt()),
            () -> assertNull(product.getUpdatedAt()),
            () -> assertNull(product.getInternalVersion()),
            () -> assertNull(product.getCreatedBy()),
            () -> assertNull(product.getUpdatedBy())
        );
    }
}
