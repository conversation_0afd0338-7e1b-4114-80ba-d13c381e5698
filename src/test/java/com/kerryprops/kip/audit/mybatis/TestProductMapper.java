package com.kerryprops.kip.audit.mybatis;

import org.apache.ibatis.annotations.*;
import org.springframework.lang.Nullable;

import java.util.List;

/**
 * 测试用的产品Mapper接口，用于演示MyBatis审计功能
 * 
 * <AUTHOR>
 */
@Mapper
public interface TestProductMapper {

    /**
     * 插入产品
     */
    @Insert("INSERT INTO test_products (name, description, price, status, created_at, updated_at, created_by, updated_by) " +
            "VALUES (#{name}, #{description}, #{price}, #{status}, #{createdAt}, #{updatedAt}, #{createdBy}, #{updatedBy})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertProduct(TestProduct product);

    /**
     * 更新产品
     */
    @Update("UPDATE test_products SET name = #{name}, description = #{description}, price = #{price}, " +
            "status = #{status}, updated_at = #{updatedAt}, updated_by = #{updatedBy} WHERE id = #{id}")
    int updateProduct(TestProduct product);

    /**
     * 删除产品
     */
    @Delete("DELETE FROM test_products WHERE id = #{id}")
    int deleteProduct(@Nullable Long id);

    /**
     * 根据ID查询产品（不会触发审计）
     */
    @Select("SELECT * FROM test_products WHERE id = #{id}")
    @Nullable
    TestProduct selectById(@Nullable Long id);

    /**
     * 查询所有产品（不会触发审计）
     */
    @Select("SELECT * FROM test_products")
    List<TestProduct> selectAll();

    /**
     * 批量插入产品
     */
    @Insert("<script>" +
            "INSERT INTO test_products (name, description, price, status, created_at, updated_at, created_by, updated_by) VALUES " +
            "<foreach collection='products' item='product' separator=','>" +
            "(#{product.name}, #{product.description}, #{product.price}, #{product.status}, " +
            "#{product.createdAt}, #{product.updatedAt}, #{product.createdBy}, #{product.updatedBy})" +
            "</foreach>" +
            "</script>")
    int batchInsertProducts(@Param("products") List<TestProduct> products);

    /**
     * 批量更新产品状态
     */
    @Update("<script>" +
            "<foreach collection='products' item='product' separator=';'>" +
            "UPDATE test_products SET status = #{product.status}, updated_at = #{product.updatedAt}, " +
            "updated_by = #{product.updatedBy} WHERE id = #{product.id}" +
            "</foreach>" +
            "</script>")
    int batchUpdateProductStatus(@Param("products") List<TestProduct> products);

    /**
     * 根据状态删除产品
     */
    @Delete("DELETE FROM test_products WHERE status = #{status}")
    int deleteByStatus(@Param("status") @Nullable String status);
}
