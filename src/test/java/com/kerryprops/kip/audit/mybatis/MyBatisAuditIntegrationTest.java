package com.kerryprops.kip.audit.mybatis;

import com.kerryprops.kip.audit.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.lang.Nullable;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * MyBatis审计集成测试
 *
 * <AUTHOR> <PERSON>
 */
@SpringBootTest(classes = MyBatisAuditIntegrationTest.TestApplication.class)
@ActiveProfiles("mybatis-test")
@TestPropertySource(properties = {
    "audit.mybatis.enabled=true",
    "audit.mybatis.enable-detailed-logging=true",
    "audit.mybatis.enable-batch-audit=true",
    "audit.mybatis.max-batch-size=100",
    "spring.application.name=mybatis-test-app",
    "audit.service-id=mybatis-test-service"
})
@Transactional
class MyBatisAuditIntegrationTest {

    @Autowired
    private TestProductMapper productMapper;

    @Autowired
    private AuditService auditService;

    @Autowired
    private MyBatisAuditInterceptor mybatisAuditInterceptor;

    @Autowired
    private MyBatisEntityExtractor entityExtractor;

    @Autowired
    private MyBatisAuditProperties mybatisAuditProperties;

    private TestProduct testProduct;

    @BeforeEach
    void setUp() {
        // 设置测试用户上下文
        XuserInfo testUser = new XuserInfo();
        testUser.setUserId(123L);
        testUser.setNickName("测试用户");
        AuditContext.storeCurrentUser(testUser);

        // 设置测试请求信息
        AuditRequestInfo requestInfo = new AuditRequestInfo();
        requestInfo.setIpAddress("127.0.0.1");
        requestInfo.setUserAgent("Test-Agent");
        requestInfo.setConversationId("test-conversation");
        requestInfo.setCorrelationId("test-correlation");
        requestInfo.setUiModel("test-ui");
        requestInfo.setAuditFilterKey("testKey");
        requestInfo.setAuditFilterValue("testValue");
        AuditContext.storeAuditRequestInfo(requestInfo);

        // 创建测试产品
        testProduct = new TestProduct();
        testProduct.setName("测试产品");
        testProduct.setDescription("这是一个测试产品");
        testProduct.setPrice(new BigDecimal("99.99"));
        testProduct.setStatus("ACTIVE");
        testProduct.setCreatedAt(LocalDateTime.now());
        testProduct.setUpdatedAt(LocalDateTime.now());
        testProduct.setCreatedBy(123L);
        testProduct.setUpdatedBy(123L);
        testProduct.setInternalVersion(1);
    }

    @Test
    void testMyBatisAuditInterceptorExists() {
        assertNotNull(mybatisAuditInterceptor, "MyBatis audit interceptor should be created");
        assertNotNull(entityExtractor, "MyBatis entity extractor should be created");
        assertNotNull(mybatisAuditProperties, "MyBatis audit properties should be created");
    }

    @Test
    void testMyBatisAuditProperties() {
        assertTrue(mybatisAuditProperties.isEnabled(), "MyBatis audit should be enabled");
        assertTrue(mybatisAuditProperties.isEnableBatchAudit(), "Batch audit should be enabled");
        assertEquals(100, mybatisAuditProperties.getMaxBatchSize(), "Max batch size should be 100");
        assertTrue(mybatisAuditProperties.isEnableDetailedLogging(), "Detailed logging should be enabled");
    }

    @Test
    void testEntityExtraction() {
        // 测试直接实体提取 - 不需要MappedStatement参数
        Object extractedEntity = entityExtractor.extractEntity(testProduct, null);
        assertNotNull(extractedEntity, "Should extract entity from direct parameter");
        assertEquals(testProduct, extractedEntity, "Extracted entity should be the same as input");

        // 测试从Map中提取实体
        java.util.Map<String, Object> paramMap = new java.util.HashMap<>();
        paramMap.put("entity", testProduct);
        paramMap.put("other", "value");

        Object extractedFromMap = entityExtractor.extractEntity(paramMap, null);
        assertNotNull(extractedFromMap, "Should extract entity from Map parameter");
        assertEquals(testProduct, extractedFromMap, "Extracted entity should be the same as input");
        System.out.println("extractedFromMap: " + extractedFromMap);
    }

    @Test
    void testInsertProductAudit() {
        // 执行插入操作
        int result = productMapper.insertProduct(testProduct);
        
        assertEquals(1, result, "Insert should affect 1 row");
        assertNotNull(testProduct.getId(), "Product ID should be generated");
        
        // 验证审计记录被创建
        // 注意：由于使用了异步处理，这里主要验证方法被调用
        // 实际的审计记录会在后台异步处理
    }

    @Test
    void testUpdateProductAudit() {
        // 先插入产品
        productMapper.insertProduct(testProduct);
        assertNotNull(testProduct.getId(), "Product should be inserted with ID");

        // 修改产品信息
        testProduct.setName("更新后的产品名称");
        testProduct.setPrice(new BigDecimal("199.99"));
        testProduct.setStatus("INACTIVE");
        testProduct.setUpdatedAt(LocalDateTime.now());

        // 执行更新操作
        int result = productMapper.updateProduct(testProduct);
        
        assertEquals(1, result, "Update should affect 1 row");
        
        // 验证审计记录被创建
        // 注意：由于使用了异步处理，这里主要验证方法被调用
    }

    @Test
    void testDeleteProductAudit() {
        // 先插入产品
        productMapper.insertProduct(testProduct);
        assertNotNull(testProduct.getId(), "Product should be inserted with ID");

        // 执行删除操作
        int result = productMapper.deleteProduct(testProduct.getId());
        
        assertEquals(1, result, "Delete should affect 1 row");
        
        // 验证审计记录被创建
        // 注意：由于使用了异步处理，这里主要验证方法被调用
    }

    @Test
    void testSelectOperationsNotAudited() {
        // 先插入产品
        productMapper.insertProduct(testProduct);
        
        // 执行查询操作（不应触发审计）
        TestProduct found = productMapper.selectById(testProduct.getId());
        assertNotNull(found, "Product should be found");
        assertEquals(testProduct.getName(), found.getName(), "Product name should match");

        // 查询所有产品（不应触发审计）
        List<TestProduct> allProducts = productMapper.selectAll();
        assertFalse(allProducts.isEmpty(), "Should find at least one product");
    }

    @Test
    void testBatchOperationsAudit() {
        // 创建多个测试产品
        TestProduct product1 = createTestProduct("产品1", new BigDecimal("10.00"));
        TestProduct product2 = createTestProduct("产品2", new BigDecimal("20.00"));
        TestProduct product3 = createTestProduct("产品3", new BigDecimal("30.00"));
        
        List<TestProduct> products = Arrays.asList(product1, product2, product3);

        // 执行批量插入
        int result = productMapper.batchInsertProducts(products);
        assertEquals(3, result, "Batch insert should affect 3 rows");
        
        // 验证批量审计记录被创建
        // 注意：由于使用了异步处理，这里主要验证方法被调用
    }

    @Test
    void testExcludedMapperMethods() {
        // 测试排除的方法模式
        assertTrue(mybatisAuditProperties.isMapperMethodExcluded("com.test.UserMapper.selectByName"));
        assertTrue(mybatisAuditProperties.isMapperMethodExcluded("com.test.UserMapper.findByEmail"));
        assertTrue(mybatisAuditProperties.isMapperMethodExcluded("com.test.UserMapper.countByStatus"));
        
        // 测试不排除的方法
        assertFalse(mybatisAuditProperties.isMapperMethodExcluded("com.test.UserMapper.insertUser"));
        assertFalse(mybatisAuditProperties.isMapperMethodExcluded("com.test.UserMapper.updateUser"));
        assertFalse(mybatisAuditProperties.isMapperMethodExcluded("com.test.UserMapper.deleteUser"));
    }

    private TestProduct createTestProduct(String name, BigDecimal price) {
        TestProduct product = new TestProduct();
        product.setName(name);
        product.setDescription("测试产品描述");
        product.setPrice(price);
        product.setStatus("ACTIVE");
        product.setCreatedAt(LocalDateTime.now());
        product.setUpdatedAt(LocalDateTime.now());
        product.setCreatedBy(123L);
        product.setUpdatedBy(123L);
        product.setInternalVersion(1);
        return product;
    }

    @SpringBootApplication(
        scanBasePackages = {"com.kerryprops.kip.audit"},
        exclude = {
            org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration.class,
            org.springframework.boot.autoconfigure.data.jpa.JpaRepositoriesAutoConfiguration.class
        }
    )
    @MapperScan("com.kerryprops.kip.audit.mybatis")
    @Import(AuditAutoConfiguration.class)
    public static class TestApplication {
        public static void main(String[] args) {
            SpringApplication.run(TestApplication.class, args);
        }
    }
}
