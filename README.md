# Audit Spring Boot Starter

[![Spring Boot](https://img.shields.io/badge/Spring%20Boot-3.2.12-brightgreen.svg)](https://spring.io/projects/spring-boot)
[![Java](https://img.shields.io/badge/Java-17-orange.svg)](https://adoptium.net/)
[![Maven](https://img.shields.io/badge/Maven-3.6+-blue.svg)](https://maven.apache.org/)

## 概述

企业级 MyBatis 审计 Spring Boot Starter，提供字段级别的实体变更追踪功能。该组件通过异步消息队列将审计记录推送到远程审计服务，支持高性能、低侵入的审计需求。

## 核心特性

- 🔍 **字段级变更追踪**: 精确捕获实体字段的变更前后值
- ⚡ **异步审计处理**: 通过内存队列异步处理，不阻塞业务流程
- 🎯 **批量推送优化**: 定时批量推送审计记录，减少网络开销
- 🚀 **高性能缓存**: 使用 Caffeine 缓存优化实体状态存储
- 🔐 **用户上下文管理**: 自动获取和管理用户信息
- 📊 **HTTP 请求日志**: 集成 Logbook 记录审计服务调用
- 🛠️ **灵活配置**: 支持排除字段、自定义推送频率等配置

## 快速开始

### 依赖引入

```xml
<dependency>
    <groupId>com.kerryprops.kip</groupId>
    <artifactId>audit-spring-boot-starter</artifactId>
    <version>1.0.0</version>
</dependency>
```

### 实体配置

使用 `@AuditEntity` 注解标记需要审计的实体：

```java
@AuditEntity(
    excludeFields = {"password", "internalVersion"},
    defaultInclude = true,
    description = "用户实体"
)
public class User {
    @AuditField(alias = "用户ID")
    private Long id;

    @AuditField(alias = "用户名")
    private String username;

    @AuditField(alias = "邮箱地址")
    private String email;

    private String password; // 被排除的字段

    // getter/setter...
}
```

### 应用配置

在 `application.yml` 中配置审计参数：

```yaml
audit:
  # 远程审计服务地址
  url: http://audit-service/api/audit/events/batch
  # 服务标识
  service-id: ${spring.application.name}
  # 推送定时任务表达式（默认每秒执行）
  cron: "* * * * * ?"
  # 字段值最大长度
  max-field-length: 1000
  # 排除审计的字段
  excluded-fields:
    - password
    - token
    - secret
  # 用户信息请求头配置
  header:
    user: x-user
```

### 用户上下文

确保 HTTP 请求包含用户信息头：

```http
x-user: {"userId": 123, "nickName": "张三", "fromType": "web"}
```

## 核心架构

### 审计组件

| 组件 | 职责 |
|------|------|
| `AuditableEntity` | 实体基类接口，标记需要审计的实体 |
| `AuditChangeListener` | JPA 实体监听器，监听实体生命周期事件 |
| `AuditService` | 核心审计服务，负责变更检测和审计记录创建 |
| `AuditQueue` | 审计事件队列，批量推送审计记录 |
| `AuditClient` | HTTP 客户端，与远程审计服务通信 |

### 上下文管理

| 组件 | 职责 |
|------|------|
| `AuditContext` | ThreadLocal 用户上下文管理 |
| `AuditUserContextWebFilter` | HTTP 过滤器，解析用户信息 |
| `XuserInfo` | 用户信息实体类 |
| `AuditRequestInfo` | 请求信息实体类 |

### 工作流程

1. **实体变更监听**: 通过 `@EntityListeners` 监听实体的 PreUpdate/PostPersist/PreRemove 事件
2. **状态缓存**: 在更新前将实体原始状态缓存到 Caffeine Cache（5分钟过期）
3. **变更检测**: 使用 JaVers 比较新旧实体状态，识别字段级变更
4. **审计记录创建**: 生成包含变更详情、用户信息、请求信息的审计事件
5. **队列缓冲**: 审计事件推送到内存队列，避免同步阻塞业务操作
6. **批量推送**: 定时任务批量推送队列中的审计记录到远程服务

## 配置说明

### 完整配置示例

```yaml
audit:
  # 基础配置
  enabled: true                                    # 是否启用审计功能
  url: http://audit-service/api/audit/events/batch # 远程审计服务地址
  service-id: ${spring.application.name}           # 服务标识
  
  # 队列配置
  cron: "*/5 * * * * ?"                           # 推送定时任务表达式
  queue-size: 1000                                # 队列最大容量
  
  # 字段配置
  max-field-length: 1000                          # 字段值最大长度
  excluded-fields:                                # 排除审计的字段
    - password
    - token
    - secret
    - createdAt
    - updatedAt
  
  # 请求头配置
  header:
    user: x-user                                  # 用户信息请求头名称
    correlation-id: x-correlation-id              # 关联ID请求头名称
```

### 环境变量支持

```bash
AUDIT_ENABLED=true
AUDIT_URL=http://audit-service/api/audit/events/batch
AUDIT_SERVICE_ID=my-service
AUDIT_CRON="*/10 * * * * ?"
```

## 开发指南

### 构建项目

```bash
# 编译项目
mvn clean compile

# 运行测试
mvn test

# 打包
mvn clean package

# 安装到本地仓库
mvn clean install
```

### 测试

项目使用 H2 内存数据库进行测试，支持以下测试类型：

- **单元测试**: 测试个别组件功能
- **集成测试**: 测试完整的 Spring 上下文
- **配置测试**: 测试不同配置场景

```bash
# 运行所有测试
mvn test

# 运行特定测试
mvn test -Dtest=AuditServiceTest

# 生成测试报告
mvn surefire-report:report
```

### 调试

启用调试日志：

```yaml
logging:
  level:
    com.kerryprops.kip.audit: DEBUG
    org.zalando.logbook: TRACE  # HTTP 请求/响应日志
```

## 性能优化

### 缓存策略

- 使用 Caffeine 缓存实体原始状态（5分钟过期）
- ID 字段反射结果缓存，避免重复查找
- 批量推送减少网络调用频次

### 异步处理

- 审计操作完全异步，不影响业务性能
- 内存队列缓冲审计事件，避免阻塞
- 可配置的推送频率和批量大小

### 监控指标

建议监控以下指标：

- 审计队列长度
- 推送成功/失败率
- 审计处理延迟
- 缓存命中率

## 故障排查

### 常见问题

**Q: 审计记录没有推送到远程服务**
A: 检查网络连接、远程服务地址配置、用户上下文是否正确设置

**Q: 性能影响较大**
A: 检查推送频率配置、队列大小设置、排除不必要的字段

**Q: 用户信息获取失败**
A: 确认请求头格式正确、过滤器配置正确

### 日志分析

启用详细日志进行问题定位：

```yaml
logging:
  level:
    com.kerryprops.kip.audit.AuditService: DEBUG
    com.kerryprops.kip.audit.AuditQueue: DEBUG
    com.kerryprops.kip.audit.AuditClient: DEBUG
```

## 技术栈

- **Spring Boot**: 3.2.12
- **Java**: 17
- **MyBatis**: 数据库操作和审计拦截
- **Jackson**: JSON 序列化
- **Caffeine**: 高性能缓存
- **JaVers**: 实体比较和变更检测
- **Logbook**: HTTP 请求/响应日志
- **Maven**: 构建工具

## 许可证

本项目采用企业内部许可证，仅供内部使用。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 联系方式

如有问题或建议，请联系开发团队。